# Parameters
nc: 10  # number of classes
depth_multiple: 1.33  # model depth multiple
width_multiple: 1.25  # layer channel multiple
anchors:
  - [7,9,  9,17,  17,15,  13,27]
  - [21,28,  36,18,  23,47,  35,33]  # P3/8
  - [58,29,43,60,82,46,66,88]  # P4/16
  - [133,77,111,135,206,137,197,290]  # P5/32

# YOLOv5 v6.0 backbone
backbone:
  # [from, number, module, args]
  [[-1, 1, Conv, [64, 6, 2, 2]],  # 0-P1/2
   [-1, 1, Conv, [128, 3, 2]],  # 1-P2/4
   [-1, 3, C3, [128]],
   [-1, 1, Conv, [256, 3, 2]],  # 3-P3/8
   [-1, 6, C3, [256]],
   [-1, 1, Conv, [512, 3, 2]],  # 5-P4/16
   [-1, 9, C3, [512]],
   [-1, 1, Conv, [1024, 3, 2]],  # 7-P5/32
   [-1, 3, C3TR, [1024]],
   [-1, 1, <PERSON><PERSON><PERSON>, [1024, 5]],  # 9
  ]

# YOLOv5 v6.0 head
head:
  [[-1, 1, Conv, [512, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [[-1, 6], 1, Concat, [1]],  # cat backbone P4
   [-1, 3, C3, [512, False]],  # 13

   [-1, 1, Conv, [256, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [[-1, 4], 1, Concat, [1]],  # cat backbone P3
   [-1, 3, C3, [256, False]],  # 17 (P3/8-small)

   [ -1, 1, Conv, [ 128, 1, 1 ] ],
   [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
   [ [ -1, 2 ], 1, Concat, [ 1 ] ],  # cat backbone P2
   [-1, 1, SPP, [128, [5, 9, 13]]],
   [ -1, 3, C3, [ 128, False ] ],  #  (P2/4-xsmall)
   [-1, 1, CBAM, [128]],           # 23

   [ -1, 1, Conv, [ 128, 3, 2 ] ],
   [ [ -1, 18, 4], 1, Concat, [ 1 ] ],  # cat head P3
   [-1, 1, SPP, [256, [5, 9, 13]]],
   [ -1, 3, C3, [ 256, False ] ],  # (P3/8-small)
   [-1, 1, CBAM, [256]],          # 28

   [-1, 1, Conv, [256, 3, 2]],
   [[-1, 14, 6], 1, Concat, [1]],  # cat head P4
   [-1, 1, SPP, [512, [3, 7, 11]]],
   [-1, 3, C3, [512, False]],  #  (P4/16-medium)
   [-1, 1, CBAM, [512]],       # 33

   [-1, 1, Conv, [512, 3, 2]],
   [[-1, 10], 1, Concat, [1]],  # cat head P5
   [-1, 1, SPP, [1024, [3, 5, 7]]],
   [-1, 3, C3TR, [1024, False]],  #  (P5/32-large)
   [-1, 1, CBAM, [1024]],        # 38

   [[23, 28, 33, 38], 1, Detect, [nc,anchors]],  # Detect(P2, P3, P4, P5)
  ]
