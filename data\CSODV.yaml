
# Train/val/test sets as 1) dir: path/to/imgs, 2) file: path/to/imgs.txt, or 3) list: [path/to/imgs1, path/to/imgs2, ..]
path: /home/<USER>/datasets/CSODV_yolo  # dataset root dir
train: train.txt  # train images (relative to 'path')  10912 images
val: test.txt  # val images (relative to 'path')  4521 images
test: test.txt  # test images (optional)  4521 images

# Classes
nc: 9  # number of classes
names: ["worker", "excavator", "cement_tanker", "spraying", "bulldozer", "road_roller", "car", "truck", "crane"]
