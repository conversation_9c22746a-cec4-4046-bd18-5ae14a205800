#!/bin/bash

# HR-FPN服务器部署脚本
# 使用方法: ./deploy_to_server.sh

SERVER_IP="************"
SERVER_USER="your_username"  # 替换为您的用户名
SERVER_PATH="/home/<USER>/HR-FPN-main"
DATASET_PATH="/data/datasets/tiny_person_yolo"

echo "🚀 开始部署HR-FPN项目到服务器..."

# 1. 上传项目文件
echo "📁 上传项目文件..."
rsync -avz --progress --exclude '__pycache__' --exclude '*.pyc' --exclude 'runs/' \
    ./ $SERVER_USER@$SERVER_IP:$SERVER_PATH/

# 2. 上传数据集（如果本地有的话）
echo "📊 上传数据集..."
if [ -d "D:/CV/dataset/tiny_person_yolo" ]; then
    rsync -avz --progress D:/CV/dataset/tiny_person_yolo/ \
        $SERVER_USER@$SERVER_IP:$DATASET_PATH/
else
    echo "⚠️  本地未找到数据集，请手动上传到服务器"
fi

# 3. 在服务器上执行环境配置
echo "⚙️  配置服务器环境..."
ssh $SERVER_USER@$SERVER_IP << 'EOF'
    cd HR-FPN-main
    
    # 检查Python环境
    echo "🐍 Python版本:"
    python --version
    
    # 检查GPU
    echo "🎮 GPU状态:"
    nvidia-smi
    
    # 安装依赖
    echo "📦 安装依赖包..."
    pip install -r requirements.txt
    
    # 创建必要目录
    mkdir -p runs/train runs/val
    
    echo "✅ 服务器环境配置完成!"
EOF

echo "🎉 部署完成!"
echo "📝 接下来的步骤:"
echo "1. SSH连接到服务器: ssh $SERVER_USER@$SERVER_IP"
echo "2. 进入项目目录: cd $SERVER_PATH"
echo "3. 检查数据集路径: ls $DATASET_PATH"
echo "4. 开始训练: python train.py"
echo "5. 后台训练: nohup python train.py > training.log 2>&1 &"
