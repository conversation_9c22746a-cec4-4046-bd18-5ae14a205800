# YOLOv5 🚀 by Ultralytics, GPL-3.0 license

# Parameters
nc: 80  # number of classes
depth_multiple: 1.0  # model depth multiple
width_multiple: 1.0  # layer channel multiple
anchors:
  - [2,3, 10,13, 16,30, 33,23]  # P2/4

# YOLOv5 v6.0 backbone
backbone:
  # [from, number, module, args]
  [[-1, 1, Conv, [64, 6, 2, 2]],  # 0-P1/2
   [-1, 1, Conv, [128, 3, 2]],  # 1-P2/4
   [-1, 3, C3, [128]],
   [-1, 1, Conv, [256, 3, 2]],  # 3-P3/8
   [-1, 6, C3, [256]],
   [-1, 1, Conv, [512, 3, 2]],  # 5-P4/16
   [-1, 9, C3, [512]],
   [-1, 1, Conv, [1024, 3, 2]],  # 7-P5/32
   [-1, 3, C3, [1024]],
  ]

# YOLOv5 v6.0 FPN head
head:
  [[-1, 1, <PERSON><PERSON><PERSON>, [1024, 5]],  # 9  P5

   [-1, 1, nn.Up<PERSON><PERSON>, [None, 2, 'nearest']],
   [-1, 1, C3, [512, False]],
   [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
   [-1, 1, C3, [256, False]],
   [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
   [-1, 1, C3, [128, False]],  # 15

   [[11, 6], 1, Concat, [1]],
   [-1, 1, C3, [512, False]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [-1, 1, C3, [256, False]],
   [[-1, 13], 1, Concat, [1]],
   [-1, 1, C3, [256, False]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [-1, 1, C3, [128, False]],
   [[-1, 15], 1, Concat, [1]],
   [-1, 1, C3, [128, False]],  # 25

   [ [ 19, 4 ], 1, Concat, [ 1 ] ],
   [ -1, 1, C3, [ 256, False ] ],
   [ -1, 1, nn.Upsample, [ None, 2, 'nearest' ] ],
   [ -1, 1, C3, [ 128, False ] ],
   [ [ -1, 25 ], 1, Concat, [ 1 ] ],
   [-1, 1, C3, [128, False]],  # 31

   [2, 1, ACmix, [128, [4, 4], 8]],
   [ -1, 1, Conv, [ 128, 1, 1 ] ],  # 33
   [ [ -1, 31 ], 1, Concat, [ 1 ] ],
   [-1, 1, C3, [128, False]],  # 35

   [ [ -1, 31 ], 1, Concat, [ 1 ] ],
   [ -1, 1, C3, [ 128, False ] ],  # 37
   [ [ -1, 25 ], 1, Concat, [ 1 ] ],
   [ -1, 1, C3, [ 128, False ] ],  # 39
   [ [ -1, 15 ], 1, Concat, [ 1 ] ],
   [ -1, 1, C3, [ 128, False ] ],  # 41

   [ [ 41, 39, 37, 35 ], 1, Concat, [ 1 ] ],
   [ -1, 1, C3, [ 128, False ] ],  # 43

   [ [ 41, 39, 37, 35 ], 1, Concat, [ 1 ] ],
   [ -1, 1, C3, [ 128, False ] ],  # 45

   [ [ 43, 45 ], 1, Concat, [ 1 ] ],  # 46

   [[46], 1, STDetect, [nc, anchors]],  # Detect(P2)
  ]
