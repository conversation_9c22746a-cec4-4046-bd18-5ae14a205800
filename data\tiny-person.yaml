# YOLOv5 🚀 by Ultralytics, GPL-3.0 license
# VisDrone2019-DET dataset https://github.com/VisDrone/VisDrone-Dataset by Tianjin University
# Example usage: python train.py --data VisDrone.yaml
# parent
# ├── yolov5
# └── datasets
#     └── VisDrone  ← downloads here


# Train/val/test sets as 1) dir: path/to/imgs, 2) file: path/to/imgs.txt, or 3) list: [path/to/imgs1, path/to/imgs2, ..]
path: D:/CV/dataset/tiny_person_yolo  # dataset root dir
train: images/train  # train images (relative to 'path')  6471 images
val:  images/test  # val images (relative to 'path')  548 images
test:  images/test  # test images (optional)  1610 images

# Classes
nc: 1  # number of classes
names: ['person']
