weights: ''
cfg: models/HR-FPN.yaml
data: data\tiny-person.yaml
hyp: data\hyps\hyp.scratch.yaml
epochs: 1
batch_size: 1
imgsz: 320
rect: false
resume: false
cwloss: false
nosave: false
noval: false
noautoanchor: false
evolve: null
bucket: ''
cache: null
image_weights: false
device: cpu
multi_scale: false
single_cls: false
optimizer: SGD
sync_bn: false
workers: 0
project: runs\train
name: exp
exist_ok: false
quad: false
linear_lr: false
label_smoothing: 0.0
patience: 100
freeze:
- 0
save_period: -1
local_rank: -1
entity: null
upload_dataset: false
bbox_interval: -1
artifact_alias: latest
save_dir: runs\train\exp9
